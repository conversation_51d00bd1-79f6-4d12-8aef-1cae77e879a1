.PHONY: help dev test build docker-dev docker-build deploy clean lint fmt check

# Default target
help:
	@echo "Analysis Engine Development Commands"
	@echo "===================================="
	@echo "make dev          - Run locally with credentials"
	@echo "make test         - Run all tests"
	@echo "make build        - Build release binary"
	@echo "make docker-dev   - Run with Docker Compose"
	@echo "make docker-build - Build Docker image"
	@echo "make deploy       - Deploy to Cloud Run"
	@echo "make clean        - Clean build artifacts"
	@echo "make lint         - Run linters"
	@echo "make fmt          - Format code"
	@echo "make check        - Run pre-commit checks"

# Local development with credentials
dev:
	@echo "Starting analysis-engine in development mode..."
	@if [ ! -f "../../vibe-match-463114-dbda8d8a6cb9.json" ]; then \
		echo "ERROR: Service account key not found at ../../vibe-match-463114-dbda8d8a6cb9.json"; \
		echo "Please ensure the credentials file exists"; \
		exit 1; \
	fi
	@if [ ! -f ".env" ]; then \
		echo "Creating .env from .env.example..."; \
		cp .env.example .env; \
	fi
	GOOGLE_APPLICATION_CREDENTIALS=../../vibe-match-463114-dbda8d8a6cb9.json \
	RUST_LOG=analysis_engine=debug,tower_http=debug \
	cargo run

# Run tests
test:
	@echo "Running tests..."
	cargo test --all-features

# Build release binary
build:
	@echo "Building release binary..."
	cargo build --release

# Run with Docker Compose
docker-dev:
	@echo "Starting with Docker Compose..."
	@if [ ! -f "../../vibe-match-463114-dbda8d8a6cb9.json" ]; then \
		echo "ERROR: Service account key not found at ../../vibe-match-463114-dbda8d8a6cb9.json"; \
		echo "Please ensure the credentials file exists"; \
		exit 1; \
	fi
	docker-compose up --build

# Build Docker image
docker-build:
	@echo "Building Docker image..."
	docker build -f Dockerfile.cloudrun -t analysis-engine:latest .

# Deploy to Cloud Run
deploy:
	@echo "Deploying to Cloud Run..."
	@if [ -z "$(PROJECT_ID)" ]; then \
		echo "Using default project ID: vibe-match-463114"; \
		export PROJECT_ID=vibe-match-463114; \
	fi
	./deploy.sh

# Clean build artifacts
clean:
	@echo "Cleaning build artifacts..."
	cargo clean
	rm -rf tmp/
	docker-compose down -v 2>/dev/null || true

# Run linters
lint:
	@echo "Running linters..."
	cargo clippy -- -D warnings

# Format code
fmt:
	@echo "Formatting code..."
	cargo fmt

# Run all pre-commit checks
check: fmt lint test
	@echo "All checks passed!"

# Check authentication status
auth-check:
	@echo "Checking authentication status..."
	@if [ -f ".env" ]; then \
		source .env && curl -s http://localhost:8001/health/auth | jq .; \
	else \
		echo "No .env file found. Run 'make dev' first."; \
	fi

# View logs
logs:
	docker-compose logs -f analysis-engine

# Stop all services
stop:
	docker-compose down

# Restart services
restart: stop docker-dev