use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use std::collections::HashMap;

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct AnalysisRequest {
    /// Repository URL (HTTPS or SSH)
    pub repository_url: String,

    /// Git branch to analyze (defaults to main/master)
    pub branch: Option<String>,

    /// File patterns to include (glob patterns)
    pub include_patterns: Vec<String>,

    /// File patterns to exclude (glob patterns)
    pub exclude_patterns: Vec<String>,

    /// Analysis depth configuration
    pub analysis_depth: AnalysisDepth,

    /// Languages to analyze (empty = all supported)
    pub languages: Vec<String>,

    /// Webhook URL for completion notification
    pub webhook_url: Option<String>,

    /// Maximum analysis time in seconds (default: 300)
    pub timeout_seconds: Option<u64>,

    /// Enable pattern detection
    pub enable_patterns: bool,

    /// Enable semantic embeddings generation
    pub enable_embeddings: bool,
}

#[derive(Debug, <PERSON><PERSON>, Deserialize, Serialize)]
pub enum AnalysisDepth {
    /// Basic metrics only (fast)
    Shallow,
    /// Full analysis with patterns and embeddings
    Full,
    /// Custom configuration
    Custom {
        include_metrics: bool,
        include_patterns: bool,
        include_embeddings: bool,
        include_dependencies: bool,
    },
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct ListAnalysesParams {
    pub page: Option<u32>,
    pub per_page: Option<u32>,
    pub status: Option<AnalysisStatus>,
    pub repository_url: Option<String>,
    pub created_after: Option<DateTime<Utc>>,
    pub created_before: Option<DateTime<Utc>>,
}

impl AnalysisRequest {
    pub fn validate(&self) -> Result<(), String> {
        if self.repository_url.is_empty() {
            return Err("Repository URL is required".to_string());
        }

        if !self.repository_url.starts_with("https://") && !self.repository_url.starts_with("git@") {
            return Err("Repository URL must be HTTPS or SSH format".to_string());
        }

        if let Some(timeout) = self.timeout_seconds {
            if timeout > 3600 {
                return Err("Timeout cannot exceed 1 hour".to_string());
            }
        }

        Ok(())
    }
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct AnalysisResult {
    /// Unique analysis identifier
    pub id: String,

    /// Repository information
    pub repository_url: String,
    pub branch: String,
    pub commit_hash: Option<String>,
    pub repository_size_bytes: Option<u64>,
    pub clone_time_ms: Option<u64>,

    /// Analysis status and timing
    pub status: AnalysisStatus,
    pub started_at: DateTime<Utc>,
    pub completed_at: Option<DateTime<Utc>>,
    pub duration_seconds: Option<u64>,

    /// Progress tracking
    pub progress: Option<f64>,
    pub current_stage: Option<String>,
    pub estimated_completion: Option<DateTime<Utc>>,

    /// Results
    pub metrics: Option<RepositoryMetrics>,
    pub patterns: Vec<DetectedPattern>,
    pub languages: HashMap<String, LanguageStats>,
    pub embeddings: Option<CodeEmbeddings>,

    /// Error handling
    pub error_message: Option<String>,
    pub failed_files: Vec<FailedFile>,

    /// File analysis results
    pub successful_analyses: Option<Vec<FileAnalysis>>,

    /// Metadata
    pub user_id: String,
    pub webhook_url: Option<String>,
    pub file_count: usize,
    pub success_rate: f64,
    
    /// Performance metrics
    pub performance_metrics: Option<PerformanceMetrics>,
    
    /// Warnings collected during analysis
    pub warnings: Vec<AnalysisWarning>,
}

impl Default for AnalysisResult {
    fn default() -> Self {
        Self {
            id: String::new(),
            repository_url: String::new(),
            branch: "main".to_string(),
            status: AnalysisStatus::Pending,
            started_at: chrono::Utc::now(),
            completed_at: None,
            duration_seconds: None,
            progress: None,
            current_stage: None,
            estimated_completion: None,
            metrics: None,
            patterns: vec![],
            languages: HashMap::new(),
            embeddings: None,
            error_message: None,
            failed_files: vec![],
            successful_analyses: None,
            user_id: String::new(),
            webhook_url: None,
            file_count: 0,
            success_rate: 0.0,
            commit_hash: None,
            repository_size_bytes: None,
            clone_time_ms: None,
            performance_metrics: None,
            warnings: vec![],
        }
    }
}

#[derive(Debug, Clone, Deserialize, Serialize, PartialEq, Eq)]
pub enum AnalysisStatus {
    Pending,
    InProgress,
    Completed,
    Failed,
    Cancelled,
    Timeout,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct RepositoryMetrics {
    /// Basic counts
    pub total_files: usize,
    pub total_lines: usize,
    pub lines_of_code: usize,
    pub comment_lines: usize,
    pub blank_lines: usize,

    /// Complexity metrics
    pub cyclomatic_complexity: f64,
    pub cognitive_complexity: f64,
    pub maintainability_index: f64,

    /// Quality metrics
    pub code_duplication_percentage: f64,
    pub test_coverage_estimate: Option<f64>,
    pub documentation_coverage: f64,

    /// Architecture metrics
    pub module_count: usize,
    pub function_count: usize,
    pub class_count: usize,
    pub interface_count: usize,

    /// Dependency metrics
    pub external_dependencies: Vec<Dependency>,
    pub internal_dependencies: Vec<InternalDependency>,
    pub dependency_depth: usize,
    pub circular_dependencies: Vec<CircularDependency>,

    /// Security metrics
    pub potential_vulnerabilities: Vec<SecurityIssue>,
    pub security_score: f64,

    /// Performance indicators
    pub large_files: Vec<LargeFile>,
    pub complex_functions: Vec<ComplexFunction>,
    pub code_smells: Vec<CodeSmell>,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct LanguageStats {
    pub name: String,
    pub lines_of_code: usize,
    pub comment_lines: usize,
    pub blank_lines: usize,
    pub total_lines: usize,
    pub file_count: usize,
    pub percentage: f64,
    pub files: Vec<String>,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct DetectedPattern {
    pub id: String,
    pub name: String,
    pub description: String,
    pub pattern_type: PatternType,
    pub confidence: f64,
    pub occurrences: Vec<PatternOccurrence>,
    pub severity: PatternSeverity,
    pub recommendation: Option<String>,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub enum PatternType {
    DesignPattern,
    AntiPattern,
    SecurityPattern,
    PerformancePattern,
    ArchitecturalPattern,
    CodeSmell,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub enum PatternSeverity {
    Info,
    Warning,
    Error,
    Critical,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct PatternOccurrence {
    pub file_path: String,
    pub line_start: usize,
    pub line_end: usize,
    pub code_snippet: String,
    pub context: HashMap<String, String>,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct FailedFile {
    pub file_path: String,
    pub error_message: String,
    pub error_type: FileErrorType,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub enum FileErrorType {
    ParseError,
    UnsupportedLanguage,
    FileTooLarge,
    PermissionDenied,
    BinaryFile,
    Timeout,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct ProgressUpdate {
    pub analysis_id: String,
    pub progress: f64,
    pub stage: String,
    pub message: Option<String>,
    pub timestamp: DateTime<Utc>,
    pub files_processed: Option<usize>,
    pub total_files: Option<usize>,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct AnalysisResponse {
    pub analysis_id: String,
    pub status: AnalysisStatus,
    pub created_at: DateTime<Utc>,
    pub estimated_completion: Option<DateTime<Utc>>,
    pub repository: RepositoryInfo,
    pub webhook_url: Option<String>,
    pub progress_url: Option<String>,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct RepositoryInfo {
    pub url: String,
    pub branch: String,
    pub commit_sha: Option<String>,
    pub size_bytes: Option<u64>,
}

// Additional models for completeness

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct Dependency {
    pub name: String,
    pub version: String,
    pub package_manager: String,
    pub is_dev_dependency: bool,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct InternalDependency {
    pub from_module: String,
    pub to_module: String,
    pub dependency_type: String,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct CircularDependency {
    pub modules: Vec<String>,
    pub cycle_type: String,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct SecurityIssue {
    pub issue_type: String,
    pub severity: String,
    pub file_path: String,
    pub line_number: usize,
    pub description: String,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct LargeFile {
    pub file_path: String,
    pub size_bytes: u64,
    pub line_count: usize,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct ComplexFunction {
    pub function_name: String,
    pub file_path: String,
    pub complexity: f64,
    pub line_count: usize,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct CodeSmell {
    pub smell_type: String,
    pub file_path: String,
    pub line_number: usize,
    pub description: String,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct CodeEmbeddings {
    pub embeddings: HashMap<String, Vec<f32>>,
    pub model_version: String,
    pub generated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct PerformanceMetrics {
    pub ast_parsing_ms: u64,
    pub embedding_generation_ms: u64,
    pub pattern_detection_ms: u64,
    pub total_analysis_ms: u64,
    pub memory_peak_mb: u64,
    pub cpu_usage_percent: Option<f64>,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct AnalysisWarning {
    pub code: String,
    pub message: String,
    pub file_path: Option<String>,
    pub severity: WarningSeverity,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub enum WarningSeverity {
    Info,
    Warning,
    Error,
}