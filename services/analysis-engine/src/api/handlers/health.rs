use axum::{
    extract::State,
    http::<PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
    response::IntoResponse,
};
use serde::{Deserialize, Serialize};
use crate::api::AppState;
use std::{env, collections::HashMap};

#[derive(Debug, Serialize, Deserialize)]
pub struct HealthResponse {
    pub status: String,
    pub service: String,
    pub version: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ReadyResponse {
    pub ready: bool,
    pub service: String,
    pub checks: HealthChecks,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct HealthChecks {
    pub spanner: bool,
    pub storage: bool,
    pub pubsub: bool,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AuthStatusResponse {
    pub auth_method: String,
    pub project_id: String,
    pub environment: String,
    pub credentials_configured: bool,
    pub service_availability: HashMap<String, bool>,
    pub debug_info: HashMap<String, String>,
}

// GET /health - Basic health check
pub async fn health() -> impl IntoResponse {
    Json(HealthResponse {
        status: "healthy".to_string(),
        service: "analysis-engine".to_string(),
        version: env!("CARGO_PKG_VERSION").to_string(),
    })
}

// GET /ready - Readiness check with dependency validation
pub async fn ready(State(state): State<AppState>) -> impl IntoResponse {
    // Check Spanner connectivity
    let spanner_ok = check_spanner(&state).await;
    
    // Check Storage connectivity
    let storage_ok = check_storage(&state).await;
    
    // Check Pub/Sub connectivity
    let pubsub_ok = check_pubsub(&state).await;

    let all_ready = spanner_ok && storage_ok && pubsub_ok;

    let response = ReadyResponse {
        ready: all_ready,
        service: "analysis-engine".to_string(),
        checks: HealthChecks {
            spanner: spanner_ok,
            storage: storage_ok,
            pubsub: pubsub_ok,
        },
    };

    if all_ready {
        (StatusCode::OK, Json(response))
    } else {
        (StatusCode::SERVICE_UNAVAILABLE, Json(response))
    }
}

// GET /metrics - Prometheus metrics endpoint
pub async fn metrics() -> impl IntoResponse {
    use prometheus::{Encoder, TextEncoder};
    
    let encoder = TextEncoder::new();
    let metric_families = prometheus::gather();
    let mut buffer = vec![];
    
    match encoder.encode(&metric_families, &mut buffer) {
        Ok(_) => (
            StatusCode::OK,
            [(axum::http::header::CONTENT_TYPE, "text/plain; version=0.0.4")],
            buffer,
        ),
        Err(_) => (
            StatusCode::INTERNAL_SERVER_ERROR,
            [(axum::http::header::CONTENT_TYPE, "text/plain")],
            b"Failed to encode metrics".to_vec(),
        ),
    }
}

async fn check_spanner(state: &AppState) -> bool {
    // Simple health check - try to execute a basic query
    match &state.spanner {
        Some(spanner) => match spanner.health_check().await {
            Ok(_) => true,
            Err(e) => {
                tracing::warn!("Spanner health check failed: {}", e);
                false
            }
        },
        None => {
            tracing::warn!("Spanner not configured - running in memory mode");
            false
        }
    }
}

async fn check_storage(state: &AppState) -> bool {
    // Simple health check - try to check bucket access
    match state.storage.health_check().await {
        Ok(_) => true,
        Err(e) => {
            tracing::warn!("Storage health check failed: {}", e);
            false
        }
    }
}

async fn check_pubsub(state: &AppState) -> bool {
    // Simple health check - try to check topic access
    match state.pubsub.health_check().await {
        Ok(_) => true,
        Err(e) => {
            tracing::warn!("PubSub health check failed: {}", e);
            false
        }
    }
}

// GET /health/auth - Authentication debugging endpoint
pub async fn auth_status(State(state): State<AppState>) -> impl IntoResponse {
    let mut debug_info = HashMap::new();
    let mut service_availability = HashMap::new();
    
    // Detect authentication method
    let auth_method = if let Ok(creds_path) = env::var("GOOGLE_APPLICATION_CREDENTIALS") {
        if !creds_path.is_empty() {
            debug_info.insert("credentials_path".to_string(), creds_path.clone());
            debug_info.insert("credentials_exists".to_string(), 
                std::path::Path::new(&creds_path).exists().to_string());
            "service_account_file".to_string()
        } else {
            "default_credentials".to_string()
        }
    } else {
        "metadata_server_or_gcloud".to_string()
    };
    
    // Check environment
    let environment = env::var("ENVIRONMENT")
        .or_else(|_| env::var("ENV"))
        .unwrap_or_else(|_| "unknown".to_string());
    
    // Check emulator configuration
    if let Ok(spanner_emu) = env::var("SPANNER_EMULATOR_HOST") {
        debug_info.insert("spanner_emulator".to_string(), spanner_emu);
    }
    if let Ok(storage_emu) = env::var("STORAGE_EMULATOR_HOST") {
        debug_info.insert("storage_emulator".to_string(), storage_emu);
    }
    if let Ok(pubsub_emu) = env::var("PUBSUB_EMULATOR_HOST") {
        debug_info.insert("pubsub_emulator".to_string(), pubsub_emu);
    }
    
    // Check service availability
    service_availability.insert("spanner".to_string(), state.spanner.is_some());
    service_availability.insert("storage".to_string(), true); // Storage is always available
    service_availability.insert("pubsub".to_string(), true); // PubSub is always available
    service_availability.insert("redis".to_string(), state.redis.is_some());
    
    // Add runtime info
    debug_info.insert("service_version".to_string(), env!("CARGO_PKG_VERSION").to_string());
    
    Json(AuthStatusResponse {
        auth_method,
        project_id: state.config.gcp.project_id.clone(),
        environment,
        credentials_configured: env::var("GOOGLE_APPLICATION_CREDENTIALS").is_ok(),
        service_availability,
        debug_info,
    })
}