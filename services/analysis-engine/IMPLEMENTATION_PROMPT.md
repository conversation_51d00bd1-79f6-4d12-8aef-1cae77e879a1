# Analysis Engine Production Implementation Prompt

## Context
You are tasked with implementing the remaining 2% of production features for the Analysis Engine service. The service is currently 98% complete but has several TODOs, placeholder code, and missing production features that must be addressed before deployment.

## Current State
- The Analysis Engine is a Rust-based service for AST parsing and code analysis
- Core functionality is implemented and working
- Authentication, API endpoints, and basic analysis features are complete
- Several production-critical features are marked as TODO or using simplified implementations

## Your Task
Research the codebase and implement all remaining production features to achieve 100% production readiness. Focus on:

### 1. Database Schema Updates
**Location**: `src/storage/spanner.rs`
**TODOs**: Lines 330-332, 346, 352
```rust
// Current TODOs:
commit_hash: None, // TODO: Add commit_hash column to database
repository_size_bytes: None, // TODO: Add repository_size_bytes column to database
clone_time_ms: None, // TODO: Add clone_time_ms column to database
successful_analyses: None, // TODO: Store and retrieve successful analyses from database
warnings: Vec::new(), // TODO: Store and retrieve warnings from database
```

**Required Actions**:
1. Create database migration scripts in `migrations/` directory
2. Add the following columns to the `analyses` table:
   - `commit_hash STRING(40)` - Git commit hash for caching
   - `repository_size_bytes INT64` - Repository size in bytes
   - `clone_time_ms INT64` - Time taken to clone repository
   - `warnings JSON` - Array of analysis warnings
3. Update `store_analysis()` method to persist these fields
4. Update `TryFrom<Row>` implementation to read these fields
5. Create index on `commit_hash` for performance

### 2. Warning Collection System
**New Feature**: Implement comprehensive warning collection throughout the analysis pipeline

**Required Implementation**:
1. Define warning types in `src/models/mod.rs`:
```rust
#[derive(Debug, Serialize, Deserialize, Clone)]
pub enum WarningType {
    ParseError,
    UnsupportedSyntax,
    LargeFile,
    MemoryLimit,
    TimeoutRisk,
    EmbeddingFailure,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct AnalysisWarning {
    pub warning_type: WarningType,
    pub message: String,
    pub file_path: Option<String>,
    pub line_number: Option<u32>,
    pub severity: WarningSeverity,
}
```

2. Collect warnings in `src/services/analyzer.rs`:
   - Track parse errors for unsupported syntax
   - Monitor memory usage and warn when approaching limits
   - Log files skipped due to size constraints
   - Record embedding generation failures
   - Aggregate warnings at the repository level

3. Store warnings in the database and include in API responses

### 3. Repository Metadata Capture
**Location**: `src/services/analyzer.rs`
**Missing Feature**: Capture git commit hash, repository size, and clone time

**Required Implementation**:
1. Add git2 dependency to Cargo.toml: `git2 = "0.19"`
2. Implement metadata capture:
```rust
use git2::Repository;

async fn capture_repository_metadata(repo_path: &str) -> Result<RepositoryMetadata> {
    let repo = Repository::open(repo_path)?;
    
    // Get current commit hash
    let head = repo.head()?;
    let commit = head.peel_to_commit()?;
    let commit_hash = commit.id().to_string();
    
    // Calculate repository size
    let repo_size = calculate_directory_size(repo_path).await?;
    
    // Track clone time
    let clone_start = std::time::Instant::now();
    // ... cloning logic ...
    let clone_time_ms = clone_start.elapsed().as_millis() as u64;
    
    Ok(RepositoryMetadata {
        commit_hash,
        repository_size_bytes: repo_size,
        clone_time_ms,
    })
}
```

3. Integrate metadata capture into the analysis flow
4. Use commit hash for intelligent cache validation

### 4. Complete Data Persistence
**Current Issue**: Only metadata is stored in Spanner, not the full analysis results

**Required Implementation**:
1. Create additional tables for storing complete analysis data:
```sql
CREATE TABLE file_analyses (
    analysis_id STRING(36) NOT NULL,
    file_path STRING(2048) NOT NULL,
    language STRING(50),
    ast_data JSON,
    symbols JSON,
    metrics JSON,
    embeddings JSON,
    errors JSON,
    PRIMARY KEY (analysis_id, file_path),
    FOREIGN KEY (analysis_id) REFERENCES analyses(analysis_id)
) PRIMARY KEY (analysis_id, file_path),
  INTERLEAVE IN PARENT analyses ON DELETE CASCADE;
```

2. Implement methods in `SpannerOperations`:
   - `store_file_analysis()`
   - `store_pattern_details()`
   - `retrieve_complete_analysis()`

3. Update the storage flow to persist all analysis data

### 5. Production Authentication
**Location**: `src/api/middleware/auth.rs` (currently using simplified auth)
**Issue**: The current implementation has TODOs and needs production hardening

**Required Actions**:
1. Implement proper JWT validation with key rotation support
2. Add API key hashing and secure storage
3. Implement rate limiting with Redis fallback
4. Add comprehensive audit logging
5. Ensure all authentication errors are properly handled

### 6. Error Handling & Resilience
**Global Requirement**: Remove all `unwrap()` and `expect()` calls

**Required Implementation**:
1. Search for all `unwrap()` and `expect()` calls in the codebase
2. Replace with proper error handling using `Result<T, E>`
3. Implement graceful degradation for non-critical failures
4. Add circuit breakers for all external service calls (Spanner, Storage, Pub/Sub)
5. Ensure analysis continues even if some files fail

### 7. Performance Optimizations
**Target**: Handle 1M lines of code in under 5 minutes

**Required Implementation**:
1. Implement streaming for large files instead of loading into memory
2. Add memory pooling for parser instances
3. Implement backpressure mechanisms
4. Add progress tracking for long-running analyses
5. Optimize database queries with proper indexing

## Implementation Guidelines

### Code Quality Standards
- All code must have >90% test coverage
- No `unsafe` blocks without explicit justification
- All public functions must have documentation
- Use `anyhow::Result` for service-level errors
- Use `thiserror` for library-level errors

### Testing Requirements
1. Unit tests for all new functions
2. Integration tests for database operations
3. Load tests for 1M LOC target
4. Security tests for authentication/authorization

### Performance Requirements
- API response time: <100ms (p95)
- Memory usage: <4GB per analysis
- Concurrent analyses: 50+
- Analysis completion: <5 minutes for 1M LOC

## Research Steps

1. **Analyze Current Implementation**:
   - Review all files in `services/analysis-engine/src/`
   - Identify all TODO comments
   - Find all `unwrap()` and `expect()` calls
   - Locate simplified/placeholder implementations

2. **Review Dependencies**:
   - Check PRPs at `/PRPs/services/analysis-engine.md`
   - Review contracts at `/contracts/ast-output-v1.json`
   - Understand Spanner schema at `/PRPs/database/spanner-schema.md`

3. **Plan Implementation**:
   - Create a detailed implementation plan
   - Prioritize based on production criticality
   - Ensure backward compatibility

## Success Criteria

Your implementation is complete when:
1. All TODO comments are resolved
2. No `unwrap()` or `expect()` calls remain
3. All tests pass with >90% coverage
4. Load testing confirms 1M LOC handling
5. Security audit shows no vulnerabilities
6. All production features from the readiness plan are implemented

## Additional Resources

- Production Readiness Plan: `/docs/analysis-engine/production-readiness-plan.md`
- Architecture Docs: `/docs/architecture/technical-specification.md`
- Example Patterns: `/examples/analysis-engine/`

Remember: The goal is to transform the current 98% complete implementation into a 100% production-ready service that can handle enterprise-scale code analysis reliably and securely.